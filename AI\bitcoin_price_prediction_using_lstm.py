# First we will import the necessary Library

import os
import pandas as pd
import numpy as np
import math
import datetime as dt
import matplotlib.pyplot as plt

# For Evalution we will use these library

from sklearn.metrics import mean_squared_error, mean_absolute_error, explained_variance_score, r2_score
from sklearn.metrics import mean_poisson_deviance, mean_gamma_deviance, accuracy_score
from sklearn.preprocessing import MinMaxScaler

# For model building we will use these library

import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model, Model
from tensorflow.keras.layers import Dense, Dropout, Input, Average
from tensorflow.keras.layers import LSTM, GRU
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import Adam, Nadam


# For PLotting we will use these library

import matplotlib.pyplot as plt
from itertools import cycle
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

"""# 3. Loading Dataset"""

# Function to clean up the price values (remove commas and quotes)
def clean_price(price_str):
    if isinstance(price_str, str):
        return float(price_str.replace('"', '').replace(',', ''))
    return price_str

# Function to process volume data with K, M, B suffixes
def process_volume(vol_str):
    if isinstance(vol_str, str):
        if 'K' in vol_str:
            return float(vol_str.replace('K', '')) * 1000
        elif 'M' in vol_str:
            return float(vol_str.replace('M', '')) * 1000000
        elif 'B' in vol_str:
            return float(vol_str.replace('B', '')) * 1000000000
        else:
            return float(vol_str)
    return vol_str

# Function to load and preprocess data
def load_data():
    # Load our dataset
    maindf = pd.read_csv('Data/Bitcoin Historical Data.csv')

    # Clean numeric columns - they have commas and quotes
    numeric_columns = ['Price', 'Open', 'High', 'Low']
    for col in numeric_columns:
        maindf[col] = maindf[col].apply(clean_price)

    # Handle the 'Vol.' column
    maindf['Volume'] = maindf['Vol.'].apply(process_volume)

    # Convert Date to datetime
    maindf['Date'] = pd.to_datetime(maindf['Date'], format='%m/%d/%Y')

    # Since the data is in reverse chronological order (newest first), sort it chronologically
    maindf = maindf.sort_values('Date')

    print('Total number of days present in the dataset: ', maindf.shape[0])
    print('Total number of fields present in the dataset: ', maindf.shape[1])

    return maindf

# Function to create dataset for LSTM
def create_dataset(dataset, time_step=1):
    dataX, dataY = [], []
    for i in range(len(dataset)-time_step-1):
        a = dataset[i:(i+time_step), 0]
        dataX.append(a)
        dataY.append(dataset[i + time_step, 0])
    return np.array(dataX), np.array(dataY)

# Enhanced LSTM model architecture based on reference strategies
def build_enhanced_lstm_model(window_size):
    """Build enhanced LSTM model with multiple layers"""
    model = Sequential()

    # First LSTM layer with return_sequences=True
    model.add(LSTM(units=50, return_sequences=True, input_shape=(window_size, 1)))
    model.add(Dropout(0.2))

    # Second LSTM layer with return_sequences=True
    model.add(LSTM(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Third LSTM layer with return_sequences=True
    model.add(LSTM(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Fourth LSTM layer without return_sequences
    model.add(LSTM(units=50))
    model.add(Dropout(0.2))

    # Output layer
    model.add(Dense(units=1))

    return model

# Enhanced GRU model architecture
def build_enhanced_gru_model(window_size):
    """Build enhanced GRU model with multiple layers"""
    model = Sequential()

    # First GRU layer with return_sequences=True
    model.add(GRU(units=50, return_sequences=True, input_shape=(window_size, 1)))
    model.add(Dropout(0.2))

    # Second GRU layer with return_sequences=True
    model.add(GRU(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Third GRU layer with return_sequences=True
    model.add(GRU(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Fourth GRU layer without return_sequences
    model.add(GRU(units=50))
    model.add(Dropout(0.2))

    # Output layer
    model.add(Dense(units=1))

    return model

# Advanced model architecture (similar to current gold LSTM)
def build_advanced_lstm_model(window_size):
    """Build advanced LSTM model with functional API"""
    input1 = Input(shape=(window_size, 1))
    x = LSTM(units=64, return_sequences=True)(input1)
    x = Dropout(0.2)(x)
    x = LSTM(units=64, return_sequences=True)(x)
    x = Dropout(0.2)(x)
    x = LSTM(units=64)(x)
    x = Dropout(0.2)(x)
    x = Dense(32, activation='relu')(x)  # Changed from softmax to relu for regression
    dnn_output = Dense(1)(x)
    model = Model(inputs=input1, outputs=[dnn_output])

    return model

# Ensemble model combining LSTM and GRU
def build_ensemble_model(window_size):
    """Build ensemble model combining LSTM and GRU"""
    # LSTM branch
    lstm_input = Input(shape=(window_size, 1), name='lstm_input')
    lstm_x = LSTM(units=50, return_sequences=True)(lstm_input)
    lstm_x = Dropout(0.2)(lstm_x)
    lstm_x = LSTM(units=50)(lstm_x)
    lstm_x = Dropout(0.2)(lstm_x)
    lstm_output = Dense(32, activation='relu')(lstm_x)

    # GRU branch
    gru_input = Input(shape=(window_size, 1), name='gru_input')
    gru_x = GRU(units=50, return_sequences=True)(gru_input)
    gru_x = Dropout(0.2)(gru_x)
    gru_x = GRU(units=50)(gru_x)
    gru_x = Dropout(0.2)(gru_x)
    gru_output = Dense(32, activation='relu')(gru_x)

    # Combine outputs
    combined = Average()([lstm_output, gru_output])
    final_output = Dense(1)(combined)

    model = Model(inputs=[lstm_input, gru_input], outputs=final_output)

    return model

def evaluate_model(model, X, y, scaler, name=""):
    """Evaluate model performance with various metrics"""
    predictions = model.predict(X)

    # Transform back to original form
    predictions = scaler.inverse_transform(predictions)
    original_y = scaler.inverse_transform(y.reshape(-1,1))

    # Calculate various metrics
    rmse = math.sqrt(mean_squared_error(original_y, predictions))
    mae = mean_absolute_error(original_y, predictions)
    r2 = r2_score(original_y, predictions)

    print(f"\n{name} Evaluation Metrics:")
    print(f"RMSE: {rmse:.2f}")
    print(f"MAE: {mae:.2f}")
    print(f"R2 Score: {r2:.4f}")

    return rmse, mae, r2

def create_enhanced_sequences(data, window_size):
    """Create sequences for training with enhanced preprocessing"""
    X, y = [], []
    for i in range(window_size, len(data)):
        X.append(data[i-window_size:i, 0])
        y.append(data[i, 0])
    return np.array(X), np.array(y)

def ensemble_predict(models, X):
    """Make ensemble predictions using multiple models"""
    predictions = []
    for model in models:
        pred = model.predict(X, verbose=0)
        predictions.append(pred)

    # Average the predictions
    ensemble_pred = np.mean(predictions, axis=0)
    return ensemble_pred

def enhanced_predict_future(model, scaler, data, window_size, future_days, use_ensemble=False, ensemble_models=None):
    """Enhanced future prediction with optional ensemble"""
    predictions = []
    current_batch = data[-window_size:].copy()

    for i in range(future_days):
        # Reshape for prediction
        current_batch_reshaped = current_batch.reshape((1, window_size, 1))

        # Make prediction
        if use_ensemble and ensemble_models:
            pred = ensemble_predict(ensemble_models, current_batch_reshaped)
        else:
            pred = model.predict(current_batch_reshaped, verbose=0)

        predictions.append(pred[0, 0])

        # Update batch for next prediction
        current_batch = np.append(current_batch[1:], pred[0, 0])

    # Transform back to original scale
    predictions_array = np.array(predictions).reshape(-1, 1)
    predictions_scaled = scaler.inverse_transform(predictions_array)

    return predictions_scaled.flatten()

def train_and_save_model():
    # Load the data
    maindf = load_data()

    # Prepare DataFrame similar to gold LSTM code
    closedf = maindf[['Date', 'Price']].copy()
    closedf['Date'] = pd.to_datetime(closedf['Date'])
    closedf = closedf.sort_values(by='Date', ascending=True).reset_index(drop=True)
    closedf['Price'] = closedf['Price'].astype('float64')
    dates = closedf['Date']

    # Test set: last year
    test_year = closedf['Date'].dt.year.max() - 1
    test_size = closedf[closedf['Date'].dt.year == test_year].shape[0]

    # Prepare train/test split
    price = closedf['Price']
    scaler = MinMaxScaler()
    scaler.fit(price.values.reshape(-1,1))
    window_size = 60

    print(f"Total data points: {len(price)}")
    print(f"Test size: {test_size}")
    print(f"Training size: {len(price) - test_size}")
    print(f"Window size: {window_size}")

    # Enhanced data preparation
    # Training data
    train_data = price[:-test_size]
    train_data_scaled = scaler.transform(train_data.values.reshape(-1,1))
    X_train, y_train = create_enhanced_sequences(train_data_scaled, window_size)

    # Test data
    test_data = price[-test_size-window_size:]
    test_data_scaled = scaler.transform(test_data.values.reshape(-1,1))
    X_test, y_test = create_enhanced_sequences(test_data_scaled, window_size)

    # Reshape for LSTM input
    X_train = np.reshape(X_train, (X_train.shape[0], X_train.shape[1], 1))
    X_test = np.reshape(X_test, (X_test.shape[0], X_test.shape[1], 1))
    y_train = np.reshape(y_train, (-1,1))
    y_test = np.reshape(y_test, (-1,1))

    print('X_train Shape: ', X_train.shape)
    print('y_train Shape: ', y_train.shape)
    print('X_test Shape:  ', X_test.shape)
    print('y_test Shape:  ', y_test.shape)

    # Enhanced training strategy with multiple models
    print("\n=== Training Enhanced Models ===")

    # 1. Enhanced LSTM Model
    print("\n1. Training Enhanced LSTM Model...")
    lstm_model = build_enhanced_lstm_model(window_size)
    lstm_model.compile(optimizer='adam', loss='mean_squared_error')

    # Add callbacks for better training
    early_stopping = EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True)
    reduce_lr = ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=10, min_lr=1e-7)

    lstm_history = lstm_model.fit(
        X_train, y_train,
        validation_data=(X_test, y_test),
        epochs=100,
        batch_size=128,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    # 2. Enhanced GRU Model
    print("\n2. Training Enhanced GRU Model...")
    gru_model = build_enhanced_gru_model(window_size)
    gru_model.compile(optimizer='adam', loss='mean_squared_error')

    gru_history = gru_model.fit(
        X_train, y_train,
        validation_data=(X_test, y_test),
        epochs=100,
        batch_size=128,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    # 3. Advanced LSTM Model (current architecture)
    print("\n3. Training Advanced LSTM Model...")
    advanced_model = build_advanced_lstm_model(window_size)
    advanced_model.compile(loss='mean_squared_error', optimizer='Nadam')

    advanced_history = advanced_model.fit(
        X_train, y_train,
        epochs=150,
        batch_size=32,
        validation_split=0.1,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    # Evaluate all models
    print("\n=== Model Evaluation ===")

    # LSTM evaluation
    lstm_pred = lstm_model.predict(X_test)
    lstm_rmse = np.sqrt(mean_squared_error(y_test, lstm_pred))
    print(f"Enhanced LSTM RMSE: {lstm_rmse:.4f}")

    # GRU evaluation
    gru_pred = gru_model.predict(X_test)
    gru_rmse = np.sqrt(mean_squared_error(y_test, gru_pred))
    print(f"Enhanced GRU RMSE: {gru_rmse:.4f}")

    # Advanced LSTM evaluation
    advanced_pred = advanced_model.predict(X_test)
    advanced_rmse = np.sqrt(mean_squared_error(y_test, advanced_pred))
    print(f"Advanced LSTM RMSE: {advanced_rmse:.4f}")

    # Select best model or create ensemble
    models = {
        'lstm': (lstm_model, lstm_rmse),
        'gru': (gru_model, gru_rmse),
        'advanced': (advanced_model, advanced_rmse)
    }

    best_model_name = min(models.keys(), key=lambda k: models[k][1])
    best_model = models[best_model_name][0]
    best_rmse = models[best_model_name][1]

    print(f"\nBest single model: {best_model_name} with RMSE: {best_rmse:.4f}")

    # Create ensemble prediction
    ensemble_pred = (lstm_pred + gru_pred + advanced_pred) / 3
    ensemble_rmse = np.sqrt(mean_squared_error(y_test, ensemble_pred))
    print(f"Ensemble RMSE: {ensemble_rmse:.4f}")

    # Choose final model (ensemble if better, otherwise best single model)
    if ensemble_rmse < best_rmse:
        print("Using ensemble approach for final model")
        # For ensemble, we'll save the best performing single model but use ensemble logic in prediction
        model = best_model
        history = advanced_history  # Use advanced history for plotting
        use_ensemble = True
    else:
        print(f"Using {best_model_name} as final model")
        model = best_model
        history = advanced_history if best_model_name == 'advanced' else (lstm_history if best_model_name == 'lstm' else gru_history)
        use_ensemble = False

    # Save the model and scaler
    model_dir = 'AI/model'
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)
    model_save_path = os.path.join(model_dir, 'bitcoin_lstm_model.keras')
    model.save(model_save_path)
    print(f"Model saved to {model_save_path}")
    import joblib
    scaler_save_path = os.path.join(model_dir, 'bitcoin_price_scaler.save')
    joblib.dump(scaler, scaler_save_path)
    print(f"Scaler saved to {scaler_save_path}")

    # Save additional models for ensemble if needed
    if use_ensemble:
        lstm_model.save(os.path.join(model_dir, 'bitcoin_lstm_enhanced.keras'))
        gru_model.save(os.path.join(model_dir, 'bitcoin_gru_enhanced.keras'))
        advanced_model.save(os.path.join(model_dir, 'bitcoin_advanced_lstm.keras'))
        print("Ensemble models saved for future use")

    # Plot loss curves
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    epochs = range(len(loss))
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, loss, 'r', label='Training loss')
    plt.plot(epochs, val_loss, 'b', label='Validation loss')
    plt.title('Training and validation loss')
    plt.legend(loc=0)
    plt.savefig(os.path.join(model_dir, 'loss_curves.png'))

    # Final evaluation with enhanced metrics
    from sklearn.metrics import mean_absolute_percentage_error
    test_loss = model.evaluate(X_test, y_test, verbose=0)

    # Use ensemble prediction if enabled
    if use_ensemble:
        y_pred = ensemble_predict([lstm_model, gru_model, advanced_model], X_test)
        print("Using ensemble prediction for final evaluation")
    else:
        y_pred = model.predict(X_test, verbose=0)

    MAPE = mean_absolute_percentage_error(y_test, y_pred)
    Accuracy = 1 - MAPE
    final_rmse = np.sqrt(mean_squared_error(y_test, y_pred))

    print(f"\n=== Final Model Performance ===")
    print(f"Test Loss: {test_loss:.6f}")
    print(f"Test MAPE: {MAPE:.4f}")
    print(f"Test Accuracy: {Accuracy:.4f}")
    print(f"Final RMSE: {final_rmse:.4f}")

    # Enhanced visualization
    y_test_true = scaler.inverse_transform(y_test)
    y_test_pred = scaler.inverse_transform(y_pred)

    # Create comprehensive plots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))

    # Plot 1: Full prediction comparison
    ax1.plot(dates.iloc[:-test_size], scaler.inverse_transform(train_data_scaled), color='black', lw=2, label='Training Data')
    ax1.plot(dates.iloc[-len(y_test_true):], y_test_true, color='blue', lw=2, label='Actual Test Data')
    ax1.plot(dates.iloc[-len(y_test_pred):], y_test_pred, color='red', lw=2, label='Predicted Test Data')
    ax1.set_title('Enhanced Model Performance on Bitcoin Price Prediction', fontsize=14)
    ax1.set_xlabel('Date', fontsize=12)
    ax1.set_ylabel('Price ($)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot 2: Loss curves comparison
    if 'loss' in history.history:
        ax2.plot(history.history['loss'], label='Training Loss', color='blue')
        if 'val_loss' in history.history:
            ax2.plot(history.history['val_loss'], label='Validation Loss', color='red')
        ax2.set_title('Training and Validation Loss')
        ax2.set_xlabel('Epochs')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    # Plot 3: Prediction accuracy scatter
    ax3.scatter(y_test_true, y_test_pred, alpha=0.6, color='green')
    ax3.plot([y_test_true.min(), y_test_true.max()], [y_test_true.min(), y_test_true.max()], 'r--', lw=2)
    ax3.set_xlabel('Actual Prices')
    ax3.set_ylabel('Predicted Prices')
    ax3.set_title('Actual vs Predicted Prices')
    ax3.grid(True, alpha=0.3)

    # Plot 4: Residuals
    residuals = y_test_true.flatten() - y_test_pred.flatten()
    ax4.scatter(range(len(residuals)), residuals, alpha=0.6, color='purple')
    ax4.axhline(y=0, color='red', linestyle='--')
    ax4.set_xlabel('Sample Index')
    ax4.set_ylabel('Residuals')
    ax4.set_title('Prediction Residuals')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(model_dir, 'enhanced_model_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

    return model, scaler, test_data_scaled, window_size, closedf, dates

def print_enhancement_summary():
    """Print summary of enhancements made to the model"""
    print("\n" + "="*80)
    print("ENHANCED BITCOIN PRICE PREDICTION MODEL")
    print("="*80)
    print("\nEnhancements Applied:")
    print("1. ✓ Multi-layer LSTM architecture (4 layers, 50 units each)")
    print("2. ✓ Enhanced GRU model for comparison")
    print("3. ✓ Advanced LSTM with functional API")
    print("4. ✓ Ensemble approach combining multiple models")
    print("5. ✓ Early stopping and learning rate reduction")
    print("6. ✓ Enhanced data preprocessing with better sequence creation")
    print("7. ✓ Comprehensive model evaluation and comparison")
    print("8. ✓ Advanced visualization with multiple plots")
    print("9. ✓ Ensemble prediction capability")
    print("10. ✓ Improved training strategies from reference files")
    print("\nTraining Strategy:")
    print("- Train multiple models (LSTM, GRU, Advanced LSTM)")
    print("- Compare performance using RMSE")
    print("- Select best model or use ensemble if better")
    print("- Save best model while preserving API compatibility")
    print("\nExpected Improvements:")
    print("- Better accuracy through ensemble learning")
    print("- Reduced overfitting with enhanced regularization")
    print("- More robust predictions with multiple architectures")
    print("- Better generalization with advanced training callbacks")
    print("="*80)

# Main execution
if __name__ == "__main__":
    print_enhancement_summary()
    print("\nStarting enhanced training process...")

    # Train the enhanced model
    train_and_save_model()

    print("\n" + "="*80)
    print("TRAINING COMPLETED SUCCESSFULLY!")
    print("="*80)
    print("\nModel files saved:")
    print("- Main model: AI/model/bitcoin_lstm_model.keras")
    print("- Scaler: AI/model/bitcoin_price_scaler.save")
    print("- Enhanced visualizations: AI/model/enhanced_model_analysis.png")
    print("\nThe enhanced model maintains full compatibility with existing API.")
    print("Ensemble models are saved for potential future use.")
    print("="*80)