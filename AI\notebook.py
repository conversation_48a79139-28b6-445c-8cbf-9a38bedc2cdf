# -*- coding: utf-8 -*-
"""Notebook.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/#fileId=https%3A//huggingface.co/arova-syams/Bitcoin-Price-Prediction-with-LSTM/blob/main/Notebook.ipynb

# Project Data Science: Bitcoin Price Prediction

## 1. Introduction
In this day AI have a significant impact almost in our life and work place, and for this field we will leveraging ai in sequential data development. In this Data Science Project we will do some research about BTCUSD consolidation price with Deep Learning Neural Network LSTM method using PyTorch library
### 1. 1 Project Objective
The main objective is to predict BTC price until the next months with LSTM (Long Short Term Memory) method
### 1. 2 Dataset Description
`BTC-USD.csv` is the name of the dataset, which was taken from the Kaggle Dataset and has seven columns: `Date, Open,High, Low, Close, Adj Close, Volume`. The data is a compilation of the price of Bitcoin from 2014 until early 2024, which is a changes every minute.

## 2. Data Preparation

### 2.1 Importing Libraries
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import root_mean_squared_error
from pycoingecko import CoinGeckoAPI
plt.style.use('ggplot')

"""### 2.2 Loading the Dataset"""

df = pd.read_csv('Dataset/BTC-USD.csv', parse_dates=['Date'])

"""### 2.3 Initial Data Exploration"""

# get 5 first data
df.head()

# get data information
df.info()

# to know mean, max, min, std, 25%, 50%, 75% of each column
df.describe()

"""## 3. Data Cleaning

### 3.1 Handling Missing Values
"""

#find missing valuee
df.isnull().sum()

# drop all missing values
df.dropna(inplace=True)
# check again
df.isnull().sum()

"""### 3.2 Removing Duplicate Values"""

# check duplicate values without Timestamp column
df.duplicated().sum()

"""## 4. Exploratory Data Analysis (EDA)

### 4.1 Descriptive Statistics
"""

# descriptive statistics
df.describe()

"""### 4.2 Correlation Analysis"""

# check the correlation between columns
df.corr()

# plot the correlation matrix
plt.figure(figsize=(10, 8))
sns.heatmap(df.corr(), annot=True, cmap='coolwarm', fmt='.2f')

"""### 4.3 Data Visualization"""

# visualize the closing price over time and volume
# make plot with 2 Y axis
fig, ax1 = plt.subplots(figsize=(14, 6))

# Plot Close price in left side
ax1.plot(df['Date'], df['Close'], color='blue', label='Close Price')
ax1.set_xlabel('Date')
ax1.set_ylabel('BTC Price (USD)', color='blue')
ax1.tick_params(axis='y', labelcolor='blue')

# make second Y axis for volume
# Plot Volume in right side
ax2 = ax1.twinx()
ax2.bar(df['Date'], df['Volume'], color='gray', alpha=0.3, label='Volume')
ax2.set_ylabel('Trading Volume', color='gray')
ax2.tick_params(axis='y', labelcolor='gray')

# Tambahkan judul dan legend
plt.title('Close Price and Trading Volume of Bitcoin (2014-2024)')
fig.tight_layout()
plt.show()

"""### 4.4 Distribution Analysis
- Close Price data is rigth skewed
"""

# distibution of the closing price
plt.figure(figsize=(10, 6))
sns.histplot(df['Close'], bins=50, kde=True, color='blue')
plt.title('Distribution of Bitcoin Closing Price')
plt.xlabel('Price (USD)')
plt.ylabel('Frequency')
plt.show()

"""## 5. Feature Engineering"""

# chech CUDA
torch.cuda.is_available()

"""### 5.1 Feature Selection"""

# make function for make sequence data xs and ys
def create_sequences(data, seq_length):
    xs, ys = [], []
    for i in range(len(data) - seq_length):
        x = data.iloc[i:(i + seq_length), 4]
        y = data.iloc[i + seq_length, 4]
        xs.append(x)
        ys.append(y)
    return np.array(xs), np.array(ys)

# make sequence data with 30 days
X, y = create_sequences(df, 30)

# check the shape of X and y
X.shape, y.shape

# check the first 5 data of X and y
X[:5], y[:5]

# divide the dataset into train 80% and test
train_size = int(len(X) * 0.8)

# divide the dataset into train and test
X_train = X[:train_size]
X_test = X[train_size:]

y_train = y[:train_size]
y_test = y[train_size:]

# check the shape of X_train, X_test, y_train, y_test
X_train.shape, X_test.shape, y_train.shape, y_test.shape

"""### 5.2 Feature Transformation with MinMaxScaler"""

# MinMaxScaler
scaler_X = MinMaxScaler(feature_range=(-1, 1))
scaler_y = MinMaxScaler(feature_range=(-1, 1))

# fit the scaler to the data
X_train_scaled = scaler_X.fit_transform(X_train)
X_test_scaled = scaler_X.transform(X_test)

y_train_scaled = scaler_y.fit_transform(y_train.reshape(-1, 1))
y_test_scaled = scaler_y.transform(y_test.reshape(-1, 1))

# change to tensor
X_train_tensor = torch.tensor(X_train_scaled).float()
y_train_tensor = torch.tensor(y_train_scaled).float()
X_test_tensor = torch.tensor(X_test_scaled).float()
y_test_tensor = torch.tensor(y_test_scaled).float()

# check the shape of X_train_tensor, y_train_tensor, X_test_tensor, y_test_tensor
X_train_tensor.shape, y_train_tensor.shape, X_test_tensor.shape, y_test_tensor.shape

"""### 5.3 Make Dataset and Dataloader"""

# make train andt test dataset
train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

# make dataloader
train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=64, shuffle=False)

"""## 6. Modelling using LSTM / GRU"""

# make class for LSTM
class LSTM(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers):
        super().__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, 1)

    def forward(self, x, num_layers, hidden_size):
        h0 = torch.zeros(num_layers, x.size(0), hidden_size)
        c0 = torch.zeros(num_layers, x.size(0), hidden_size)
        out, _ = self.lstm(x, (h0, c0))
        out = self.fc(out[:, -1, :])
        return out

class GRU(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers):
        super().__init__()
        self.gru = nn.GRU(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, 1)

    def forward(self, x, num_layers, hidden_size):
        h0 = torch.zeros(num_layers, x.size(0), hidden_size)
        out, _ = self.gru(x, h0)
        out = self.fc(out[:, -1, :])
        return out

"""### 6.1 Model Training Loop"""

# make LSTM model with input size 1, hidden size 64, and num layers 2
lstm = LSTM(input_size=1, hidden_size=64, num_layers=2)
# make GRU model with input size 1, hidden size 64, and num layers 2
gru = GRU(input_size=1, hidden_size=64, num_layers=2)
# use MSE loss function and Adam optimizer
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(lstm.parameters(), lr=0.001)

# make training iteration
num_epochs = 100

for epoch in range(num_epochs):
    # for epoch in range(num_epochs):
    for seqs, labels in train_loader:
        # reshape the data
        seqs = seqs.view(seqs.size(0), seqs.size(1), 1)
        # make prediction
        y_pred = lstm(seqs, num_layers=2, hidden_size=64)
        # calculate the loss
        loss = criterion(y_pred, labels.view(-1, 1))
        # backpropagation
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        print(f'Epoch {epoch+1}/{num_epochs}, Loss: {loss.item():.4f}')

"""### 6.2 Model Evaluation Loop"""

# mse = torchmetrics.MeanSquaredError()

# set the model to evaluation mode
lstm.eval()
# make prediction
with torch.no_grad():
    for seqs, labels in test_loader:
        seqs = seqs.view(seqs.size(0), seqs.size(1), 1)
        y_pred_log = lstm(seqs, num_layers=2, hidden_size=64)
        # invers log transformation
        y_pred = scaler_y.inverse_transform(y_pred_log.detach().cpu().numpy())
        y_true = scaler_y.inverse_transform(labels.view(-1, 1).cpu().numpy())
        rmse = root_mean_squared_error(y_pred, y_true)

#rmse = sklearn.sqrt(mse.nump)
print(f"Test RMSE: {rmse}")

"""LSTM Test RMSE: 1118.783447265625  
GRU Test RMSE: 21445.294921875
"""

# plot y_pred and y_true
plt.figure(figsize=(14, 6))
plt.plot(y_true, label='True Price', color='blue')
plt.plot(y_pred, label='Predicted Price', color='red')
plt.title('True Price vs Predicted Price')

"""## 7. Prediction of Data"""

# make a function for prediction
def predict(model, input_seq, future_days, num_layers, hidden_size):
    # make model to be evaluation mode
    model.eval()
    preds = []

    input_seq = input_seq.view(-1, 1).clone().detach()

    for _ in range(future_days):
        # prepare input shape
        seq_input = input_seq[-60:].unsqueeze(0)

        # make prediction
        with torch.no_grad():
            y_pred_log = model(seq_input, num_layers, hidden_size)

        # save the prediction
        preds.append(y_pred_log.item())

        # update the input sequence with the prediction
        input_seq = torch.cat((input_seq, y_pred_log), dim=0)

    # invers log transformation
    #preds_tensor = torch.tensor(preds)
    #preds_tensor = torch.expm1(preds_tensor)

    preds_array = np.array(preds).reshape(-1, 1)
    preds_tensor = scaler_y.inverse_transform(preds_array)
    return preds_tensor

"""### 7.1 Predict from Existinc Data"""

# make prediction for 60 days ahead
last_seq = X_test_tensor[-60:]

predictions = predict(
    model=lstm,
    input_seq=last_seq,
    future_days=60,
    num_layers=2,
    hidden_size=64
)

# check the prediction shape
predictions.shape

# get future TRUE data from csv
btc_future = pd.read_csv('Dataset/btc_future.csv', parse_dates=['Open time'])
btc_future = btc_future.rename(columns={'Open time': 'Date'})
btc_future = btc_future[(btc_future['Date'] >= '2024-01-21') & (btc_future['Date'] <= '2024-03-21')]

# visualize the prediction
plt.figure(figsize=(12, 6))
plt.plot(df['Date'].iloc[-1000:], df['Close'].iloc[-1000:], label='Actual Price', color='blue', linewidth=1)
plt.plot(pd.date_range(start=df['Date'].iloc[-1] + pd.Timedelta(days=1), periods=60), predictions, label='Predicted Price', color='red', linewidth=2)
plt.plot(btc_future['Date'], btc_future['Close'], label='True Price', color='green', linewidth=1, alpha=0.5)
plt.title('Bitcoin Price Prediction')
plt.xlabel('Date')
plt.ylabel('Price (USD)')
plt.legend()
plt.show()

# save the model
torch.save(lstm.state_dict(), 'lstm_model.pth')

"""### 7.2 Predict Recent Price from API Data (Optional)"""

# initialize the CoinGeckoAPI
cg = CoinGeckoAPI()
# get the data from CoinGecko API
data = cg.get_coin_market_chart_by_id(id='bitcoin', vs_currency='usd', days=180)

# make dataframe from the data
prices_df = pd.DataFrame(data['prices'], columns=['timestamp', 'price'])
prices_df['timestamp'] = pd.to_datetime(prices_df['timestamp'], unit='ms')

# make prices daily
daily_prices = prices_df.resample('D', on='timestamp').agg({'price': 'last'}).reset_index()
daily_prices.head()

# change daily_price to numpy array
daily_prices_array = daily_prices['price'].values.reshape(-1, 1)

# scale the data
daily_prices_array = scaler_y.transform(daily_prices_array)
daily_prices_tensor = torch.tensor(daily_prices_array).float()

# check the shape of daily_prices_tensor
daily_prices_tensor.shape

# make prediction for 60 days ahead
predictions = predict(
    model=lstm,
    input_seq=daily_prices_tensor,
    future_days=30,
    num_layers=2,
    hidden_size=64
)

# visualize the prediction
plt.figure(figsize=(14, 6))
plt.plot(daily_prices['timestamp'], daily_prices['price'], label='CoinGecko Price', color='blue')
plt.plot(pd.date_range(start=daily_prices['timestamp'].iloc[-1] + pd.Timedelta(days=1), periods=30), predictions, label='Predicted Price', color='red')
plt.title('CoinGecko Bitcoin Price Prediction')
plt.xlabel('Date')
plt.ylabel('Price (USD)')
plt.legend()
plt.show()

"""## 7. Interpretation and Conclusion

### 7.1 Interpretation of Result
The LSTM model applied to BTC/USD price data demonstrated strong performance. Using 3,382 daily BTC data points from September 18, 2014, to January 21, 2024, along with the application of various data transformation and normalization techniques, the model was able to produce predictions that closely matched actual values.

Evaluation using the Root Mean Squared Error (RMSE) metric showed an error margin of approximately 1,600 USD, which is considered low given the volatility of BTC prices. This indicates that the LSTM model effectively learned patterns from historical data with high accuracy.

### 7.2 Conclusions and recommendation

the Conclusion from visualization Actual vs predicted price data have accurate result with the spike of BTC price from 21-01-2024 forward make this model resulted as `reliable prediction model`. For the next step may this model will need some improvement with add extra feature or another hyperparameter tuning.
"""