{"name": "btc-predict-frontend", "version": "0.1.0", "private": true, "proxy": "http://localhost:5000", "dependencies": {"@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.34", "axios": "^1.6.2", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "styled-components": "^6.1.1", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}